---
thumbnail: "/projects/guangqifengtian-car-3d-show/intro.png"
title: "GAC Toyota - Vilandara"
description: "I developed a 3D web application using the Three.js framework for GAC Toyota to showcase their 2020 model, the Venza. The project presents an interactive 3D model of the Venza, allowing users to switch vehicle colors, open the doors and windows, turn on the headlights, and simulate the car's operation. This innovative digital display provides potential customers with a more intuitive and immersive experience, enhancing the brand's image and improving customer engagement."
published: 2020/01/07
slug: "guangqi-car-3d-show"
---

![thumbnail](/projects/guangqifengtian-car-3d-show/intro.png "thumbnail")

- Customer: Guangqi Toyota Motor Co., Ltd.
- Date: 2020/01/07
- Category: 3D Web
- Responsibility: Front-end Development

## Demo video
::YoutubePlayer{videoId="lri0MMf5RYI"}
:: 

## Project Introduction
I developed a 3D web application using the Three.js framework for GAC Toyota to showcase their 2020 model, the Venza. The project presents an interactive 3D model of the Venza, allowing users to switch vehicle colors, open the doors and windows, turn on the headlights, and simulate the car's operation. This innovative digital display provides potential customers with a more intuitive and immersive experience, enhancing the brand's image and improving customer engagement.