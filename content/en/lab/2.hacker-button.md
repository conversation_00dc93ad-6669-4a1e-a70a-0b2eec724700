::<PERSON><PERSON>ard{title="Hacker button" description="Randomize text on click with vue"}

#preview
::HackerButton{label="Submit Form"}
::

#codebase
::CodeView

```vue
<template>
  <button
    type="button"
    class="rounded-md bg-white dark:bg-gray-800 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-950 relative font-mono"
    @click="startScrambling"
  >
    {{ displayText }}
  </button>
</template>

<script setup>
const props = defineProps({
  label: String,
});

const displayText = ref(props.label);
const charset = "abcdefghijklmnopqrstuvwxyz";

function randomChars(length) {
  return Array.from(
    { length },
    () => charset[Math.floor(Math.random() * charset.length)]
  ).join("");
}

async function scramble(input) {
  let prefix = "";
  for (let index = 0; index < input.length; index++) {
    await new Promise((resolve) => setTimeout(resolve, 50));
    prefix += input.charAt(index);
    displayText.value = prefix + randomChars(input.length - prefix.length);
  }
}

const startScrambling = () => {
  scramble(props.label);
  setTimeout(() => console.log("Submitted"), props.label.length * 50);
};

watch(
  () => props.label,
  (newValue) => {
    displayText.value = newValue;
  }
);
</script>
```

::

#usage
::CodeView

```vue
<HackerButton label="Submit Form' />
```

::
