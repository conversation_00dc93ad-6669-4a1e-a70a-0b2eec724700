---
thumbnail: "/projects/peekaboo/peekaboo.jpg"
title: "PEEK-A-BOO"
description: "PEEK-A-BOO 是一个将艺术与科技融合的装置艺术项目，通过结合多种技术手段，包括 Max/MSP 声音处理、Arduino 硬件控制、投影技术以及 p5.js 着色器编程，创造出一个充满创意与互动性的体验空间。该项目旨在通过实时生成和处理图形和声音，为观众提供一种沉浸式的感官体验，激发观众的创意灵感和情感共鸣。"
published: 2023/08/29
slug: "peekaboo"
---

> PEEK-A-BOO 是一个将艺术与科技融合的装置艺术项目，通过结合多种技术手段，包括 Max/MSP 声音处理、Arduino 硬件控制、投影技术以及 p5.js 着色器编程，创造出一个充满创意与互动性的体验空间。该项目旨在通过实时生成和处理图形和声音，为观众提供一种沉浸式的感官体验，激发观众的创意灵感和情感共鸣。

![thumbnail](/projects/peekaboo/peekaboo.jpg "thumbnail")

- 客户: Goldsmiths, London
- 日期: 2023/08/29
- 分类: 装置艺术
- 责任: 程序开发

## 项目故事
在一个充满科技感与艺术氛围的空间中，PEEK-A-BOO 项目将观众带入一个动态变化的世界。通过与环境的互动，观众可以看到实时生成的图形和听到独特的声音效果，仿佛进入了一个科技与艺术交织的梦幻空间。

项目灵感来源于日常生活中的“捉迷藏”游戏，象征着探索与发现的乐趣。每当观众在空间中移动或触碰装置，系统会根据其动作生成独特的图形和声音反馈，使每一刻都充满未知和惊喜。观众仿佛在与装置进行一场“捉迷藏”游戏，探索艺术与科技的无尽可能。

![thumbnail](/projects/peekaboo/p2.jpg "thumbnail")
![thumbnail](/projects/peekaboo/p3.jpg "thumbnail")
![thumbnail](/projects/peekaboo/p4.jpg "thumbnail")

## 技术难点
1. 实时声音和图形生成：需要使用 Max/MSP 进行复杂的声音处理和生成，并与 p5.js 着色器编程结合，实现实时图形的生成和变化。这要求系统具备高效的计算能力和优化算法，以保证实时性能和流畅体验。
2. 多技术集成：项目涉及多种技术，包括 Max/MSP、Arduino、投影技术和 p5.js。这些技术需要无缝集成，确保数据的实时传输和处理，同时保证系统的稳定性和可靠性。
3. 互动性设计：如何设计互动方式，使观众的每一个动作都能与系统产生有效互动，生成独特的声音和图形反馈。需要对观众行为进行精确的捕捉和分析，设计出直观且有趣的互动模式。
4. 硬件兼容性和调试：Arduino 硬件的使用需要精细的调试和编程，确保传感器数据的准确采集和响应速度。同时，投影设备的调试也至关重要，需要保证投影图像的清晰度和同步性。

![thumbnail](/projects/peekaboo/p5.jpg "thumbnail")
![thumbnail](/projects/peekaboo/p6.jpg "thumbnail")

## 创意灵感激发
PEEK-A-BOO 不仅是一个装置艺术项目，更是一个激发创意灵感的平台。通过与系统的互动，观众可以感受到科技与艺术的融合带来的无限可能。这种沉浸式的体验能够激发观众的好奇心和创造力，鼓励他们探索和尝试新的创意表达方式。

![thumbnail](/projects/peekaboo/p7.jpg "thumbnail")
![thumbnail](/projects/peekaboo/p8.jpg "thumbnail")
![thumbnail](/projects/peekaboo/p9.jpg "thumbnail")