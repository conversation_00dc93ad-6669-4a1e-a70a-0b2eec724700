{"name": "nuxt-app", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "devDependencies": {"@nuxt/content": "^2.10.0", "@nuxt/devtools": "1.0.5", "@nuxt/scripts": "^0.10.5", "@nuxthq/studio": "1.0.6", "@nuxtjs/fontaine": "^0.4.1", "@nuxtjs/google-fonts": "3.1.1", "@nuxtjs/i18n": "8.0.0", "@nuxtjs/sitemap": "^5.0.0", "@types/youtube": "^0.1.0", "nuxt": "^3.17.7", "nuxt-icon": "0.6.7", "prettier": "3.1.1", "prettier-plugin-tailwindcss": "0.5.9", "sass": "^1.89.2", "typescript": "^5.8.3", "yarn-upgrade-all": "^0.7.2"}, "dependencies": {"@iconify-json/lucide": "1.1.145", "@nuxt/image": "1.1.0", "@nuxt/ui": "^3.2.0", "vue-use-fixed-header": "^2.0.0"}, "packageManager": "yarn@1.22.21"}