<template>
  <div ref="headerRef" :style="styles" class="fixed top-0 w-full z-50">
    <nav class="mx-auto px-4 sm:px-6 lg:px-8 max-w-2xl">
      <ul
        class="flex items-center my-4 px-3 text-sm font-medium text-gray-800 rounded-full shadow-lg bg-white/90 shadow-gray-800/5 ring-1 backdrop-blur dark:bg-gray-800/90 dark:text-gray-200 dark:ring-white/20 ring-gray-900/5"
      >
        <li v-for="item in items" :key="item.path">
          <UTooltip
            :text="lang == 'zh' ? item.cnName : item.name"
            :ui="{ popper: { strategy: 'absolute' } }"
          >
            <ULink
              :to="localePath(item.path)"
              class="relative px-3 py-4 flex items-center justify-center transition hover:text-primary-500 dark:hover:text-primary-400"
              active-class="text-primary-600 dark:text-primary-400"
            >
              <Icon aria-hidden="true" :name="item.icon" class="w-5 h-5 z-10" />
              <span
                v-if="$route.path === item.path"
                class="absolute inset-x-1 -bottom-px h-px bg-gradient-to-r from-primary-500/0 via-primary-500/70 to-primary-500/0 dark:from-primary-400/0 dark:via-primary-400/40 dark:to-primary-400/0"
              ></span>
              <span
                v-if="$route.path === item.path"
                class="absolute h-8 w-8 z-0 rounded-full bg-gray-100 dark:bg-white/10 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
              ></span>
              <span class="sr-only">{{ item.name }}</span>
            </ULink>
          </UTooltip>
        </li>
        <li class="flex-1"></li>
        <li>
          <AppThemeToggle />
        </li>
        <li>
          <AppSwitchLanguage />
        </li>
      </ul>
    </nav>
  </div>
</template>

<script setup>
import { useFixedHeader } from "vue-use-fixed-header";
const headerRef = ref(null);
const { styles } = useFixedHeader(headerRef);

const items = [
  {
    name: "homepage",
    cnName: "主页",
    path: "/",
    icon: "solar:home-smile-outline",
  },
  {
    name: "project",
    cnName: "项目",
    path: "/projects",
    icon: "solar:folder-with-files-outline",
  },
  {
    name: "article",
    cnName: "文章",
    path: "/articles",
    icon: "solar:document-add-outline",
  },
  {
    name: "art",
    cnName: "艺术",
    path: "/art",
    icon: "pepicons-pencil:paint-pallet",
  },
  // { name: "实验室", path: "/lab", icon: "heroicons:beaker" },
  // {
  //   name: "What's in my bag?",
  //   path: "/whats-in-my-bag",
  //   icon: "solar:backpack-outline",
  // },
  // {
  //   name: "Bookmarks",
  //   path: "/bookmarks",
  //   icon: "solar:bookmark-linear",
  // },
];

const { locale, setLocale } = useI18n();
const localePath = useLocalePath();
const lang = computed({
  get() {
    return locale.value;
  },
  set(v) {
    setLocale(v);
  },
});
</script>
